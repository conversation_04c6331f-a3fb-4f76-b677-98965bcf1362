/*
 Navicat Premium Data Transfer

 Source Server         : ************ 测试环境
 Source Server Type    : MySQL
 Source Server Version : 50738 (5.7.38)
 Source Host           : ************:3306
 Source Schema         : security_management

 Target Server Type    : MySQL
 Target Server Version : 50738 (5.7.38)
 File Encoding         : 65001

 Date: 22/05/2025 15:11:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for common_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `common_dict_data`;
CREATE TABLE `common_dict_data` (
  `ID` varchar(20) NOT NULL COMMENT 'ID',
  `CREATED_BY` varchar(32) DEFAULT NULL COMMENT '创建人',
  `CREATED_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATED_BY` varchar(32) DEFAULT NULL COMMENT '更新人',
  `UPDATED_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DELETE_FLAG` varchar(1) NOT NULL COMMENT '是否删除;0-未删除 1-删除',
  `VERSION` int(11) NOT NULL COMMENT '版本号;每次修改都会递增',
  `DICT_ID` varchar(20) NOT NULL COMMENT '字典类型ID',
  `CODE` varchar(8) NOT NULL COMMENT 'code;字典code',
  `NAME` varchar(32) NOT NULL COMMENT '字段名称',
  `COLOR` varchar(8) DEFAULT NULL COMMENT '颜色',
  `SORT` int(11) NOT NULL COMMENT '排序',
  `STATUS` int(11) NOT NULL COMMENT '状态;默认1-启用 0-禁用',
  `REMARKS` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据字典';

-- ----------------------------
-- Records of common_dict_data
-- ----------------------------
BEGIN;
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('1', NULL, NULL, NULL, NULL, '0', 0, '1', 'workday', '工作日', NULL, 10, 0, NULL);
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('2', NULL, NULL, NULL, NULL, '0', 0, '1', 'holiday', '周末及节假日', NULL, 20, 0, NULL);
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('3', NULL, NULL, NULL, NULL, '0', 0, '2', '0', '职工', NULL, 10, 0, NULL);
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('4', NULL, NULL, NULL, NULL, '0', 0, '2', '1', '医患', NULL, 20, 0, NULL);
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('5', NULL, NULL, NULL, NULL, '0', 0, '3', '0', '启用', NULL, 10, 0, NULL);
INSERT INTO `common_dict_data` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `DICT_ID`, `CODE`, `NAME`, `COLOR`, `SORT`, `STATUS`, `REMARKS`) VALUES ('6', NULL, NULL, NULL, NULL, '0', 0, '3', '1', '禁用', NULL, 20, 0, NULL);
COMMIT;

-- ----------------------------
-- Table structure for common_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `common_dict_type`;
CREATE TABLE `common_dict_type` (
  `ID` varchar(20) NOT NULL COMMENT 'ID',
  `CREATED_BY` varchar(32) DEFAULT NULL COMMENT '创建人',
  `CREATED_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATED_BY` varchar(32) DEFAULT NULL COMMENT '更新人',
  `UPDATED_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DELETE_FLAG` varchar(1) NOT NULL COMMENT '是否删除;0-未删除 1-删除',
  `VERSION` int(11) NOT NULL COMMENT '版本号;每次修改都会递增',
  `TYPE` varchar(8) NOT NULL COMMENT '字典类型;字典类型，比如班车、合同，不能每个功能都去创建一个字典库',
  `DICT_CODE` varchar(32) NOT NULL COMMENT '字典code;字典code',
  `NAME` varchar(32) NOT NULL COMMENT '字段名称',
  `SORT` int(11) NOT NULL COMMENT '排序',
  `STATUS` int(11) NOT NULL COMMENT '状态;默认1-启用 0-禁用',
  `REMARKS` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据字典';

-- ----------------------------
-- Records of common_dict_type
-- ----------------------------
BEGIN;
INSERT INTO `common_dict_type` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `TYPE`, `DICT_CODE`, `NAME`, `SORT`, `STATUS`, `REMARKS`) VALUES ('1', NULL, NULL, NULL, '2025-05-19 15:30:55', '0', 0, 'BUS', 'BUS_TYPE', '班车类型', 10, 0, NULL);
INSERT INTO `common_dict_type` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `TYPE`, `DICT_CODE`, `NAME`, `SORT`, `STATUS`, `REMARKS`) VALUES ('2', NULL, NULL, NULL, NULL, '0', 0, 'BUS', 'PASSENGER_TYPE', '乘客类型', 20, 0, NULL);
INSERT INTO `common_dict_type` (`ID`, `CREATED_BY`, `CREATED_TIME`, `UPDATED_BY`, `UPDATED_TIME`, `DELETE_FLAG`, `VERSION`, `TYPE`, `DICT_CODE`, `NAME`, `SORT`, `STATUS`, `REMARKS`) VALUES ('3', NULL, NULL, NULL, NULL, '0', 0, 'BUS', 'STATUS', '状态', 30, 0, NULL);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
